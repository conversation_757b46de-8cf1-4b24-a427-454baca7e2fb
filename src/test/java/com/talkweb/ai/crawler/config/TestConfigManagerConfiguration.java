package com.talkweb.ai.crawler.config;

import com.talkweb.ai.crawler.config.NetworkProperties;
import com.talkweb.ai.crawler.config.StorageProperties;
import com.talkweb.ai.crawler.downloader.FileDownloader;
import com.talkweb.ai.crawler.storage.StorageManager;
import com.talkweb.ai.crawler.service.CrawlOrchestrationService;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.mockito.Mockito;
import java.util.ArrayList;

/**
 * 测试专用配置类
 * 为集成测试提供必需的bean，解决依赖注入问题
 */
@TestConfiguration
public class TestConfigManagerConfiguration {

    /**
     * 创建测试专用的ConfigManager实例
     * 使用空参数数组和默认配置文件
     *
     * @return ConfigManager实例
     */
    @Bean
    @Primary
    public ConfigManager configManager() {
        // 使用空的命令行参数和null配置文件（使用默认配置）
        return new ConfigManager(new String[0], null);
    }

    /**
     * 创建测试专用的CrawlerProperties实例
     * 避免配置文件解析问题
     *
     * @return CrawlerProperties实例
     */
    @Bean
    @Primary
    public CrawlerProperties crawlerProperties() {
        CrawlerProperties properties = new CrawlerProperties();
        properties.setUrl("https://httpbin.org/html");
        properties.setMaxDepth(2);
        properties.setThreads(1);
        properties.setAllowedDomains(new ArrayList<>());
        properties.setEnableDynamicFetcher(false);
        properties.setDownloadAttachments(false);
        properties.setExternalLinkMaxDepth(0);
        return properties;
    }

    /**
     * 创建测试专用的FileDownloader实例
     * 避免@Value注入问题
     *
     * @return FileDownloader实例
     */
    @Bean
    @Primary
    public FileDownloader fileDownloader() {
        StorageProperties storageProperties = new StorageProperties();
        storageProperties.setOutputDir("./test-output");
        storageProperties.setMaxFileSize(1048576L);

        NetworkProperties networkProperties = new NetworkProperties();
        networkProperties.setConnectionTimeout(5000);
        networkProperties.setReadTimeout(5000);
        networkProperties.setMaxRetries(1);

        return new FileDownloader(storageProperties, networkProperties);
    }

    /**
     * 创建测试专用的StorageManager实例
     *
     * @return StorageManager实例
     */
    @Bean
    @Primary
    public StorageManager storageManager() {
        return new StorageManager("./test-output");
    }

    /**
     * 创建模拟的CrawlOrchestrationService实例用于测试
     *
     * @return CrawlOrchestrationService mock实例
     */
    @Bean
    @Primary
    public CrawlOrchestrationService crawlOrchestrationService() {
        return Mockito.mock(CrawlOrchestrationService.class);
    }


    /**
     * 创建测试专用的NetworkProperties实例
     *
     * @return NetworkProperties实例
     */
    @Bean
    @Primary
    public NetworkProperties networkProperties() {
        NetworkProperties props = new NetworkProperties();
        props.setConnectionTimeout(5000);
        props.setReadTimeout(5000);
        props.setMaxRetries(1);
        props.setUserAgent("Test-Crawler/1.0");
        return props;
    }

    /**
     * 创建测试专用的StorageProperties实例
     *
     * @return StorageProperties实例
     */
    @Bean
    @Primary
    public StorageProperties storageProperties() {
        StorageProperties props = new StorageProperties();
        props.setOutputDir("./test-output");
        props.setMaxFileSize(1048576L);
        return props;
    }
}
