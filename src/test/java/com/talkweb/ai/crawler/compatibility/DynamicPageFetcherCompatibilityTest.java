package com.talkweb.ai.crawler.compatibility;

import com.talkweb.ai.crawler.config.PlaywrightProperties;
import com.talkweb.ai.crawler.fetcher.DynamicPageFetcher;
import com.talkweb.ai.crawler.fetcher.DynamicPageFetcherV2;
import com.talkweb.ai.crawler.fetcher.IFetcher;
import com.talkweb.ai.crawler.model.PageData;
import com.talkweb.ai.crawler.monitor.PerformanceMonitor;
import com.talkweb.ai.crawler.testutils.MockHttpServer;
import org.junit.jupiter.api.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DynamicPageFetcher兼容性测试
 * 验证新版本DynamicPageFetcherV2与旧版本DynamicPageFetcher的功能一致性
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
//@Disabled("Compatibility tests require Playwright browser - too slow for CI")
class DynamicPageFetcherCompatibilityTest {

    private static final Logger logger = LoggerFactory.getLogger(DynamicPageFetcherCompatibilityTest.class);

    private static MockHttpServer mockServer;
    private static String baseUrl;

    private DynamicPageFetcher legacyFetcher;
    private DynamicPageFetcherV2 newFetcher;
    private PlaywrightProperties properties;
    private PerformanceMonitor performanceMonitor;

    @BeforeAll
    static void setUpClass() throws Exception {
        // 启动模拟HTTP服务器
        mockServer = new MockHttpServer(8082); // 指定端口
        mockServer.start();
        baseUrl = mockServer.getBaseUrl();

        // 配置测试页面
        mockServer.addHtmlResponse("/simple", "简单页面", "测试内容");
        mockServer.addHtmlResponse("/complex", "复杂页面", "这是一个复杂的页面，包含动态内容");
        mockServer.addErrorResponse("/error", 404, "Not Found");
        mockServer.addSlowResponse("/slow", 2000,
            "<html><head><title>慢页面</title></head><body><h1>慢加载</h1></body></html>");

        logger.info("Mock server started at: {}", baseUrl);
    }

    @AfterAll
    static void tearDownClass() {
        if (mockServer != null) {
            mockServer.stop();
        }
    }

    @BeforeEach
    void setUp() {
        // 创建通用配置
        properties = new PlaywrightProperties();
        properties.setBrowserType("chromium");
        properties.setHeadless(true);
        properties.setTimeout(10000);
        properties.setRetries(2);
        properties.setPoolSize(2);
        properties.setViewportWidth(1280);
        properties.setViewportHeight(720);
        properties.setWaitForNetworkIdle(false);
        properties.setExtraWaitTime(500);

        performanceMonitor = new PerformanceMonitor();

        // 创建新旧两个版本的fetcher
        legacyFetcher = new DynamicPageFetcher(
            properties.getBrowserType(),
            properties.isHeadless(),
            properties.getTimeout(),
            properties.getRetries(),
            properties.getPoolSize()
        );

        // 手动初始化DynamicPageFetcher（因为@PostConstruct在测试中不会自动调用）
        try {
            java.lang.reflect.Method initMethod = DynamicPageFetcher.class.getDeclaredMethod("initializeBrowserPool");
            initMethod.setAccessible(true);
            initMethod.invoke(legacyFetcher);
        } catch (Exception e) {
            throw new RuntimeException("Failed to initialize legacy fetcher", e);
        }

        newFetcher = new DynamicPageFetcherV2(properties, performanceMonitor);

        // 手动初始化DynamicPageFetcherV2（因为@PostConstruct在测试中不会自动调用）
        newFetcher.initialize();
    }

    @AfterEach
    void tearDown() {
        if (legacyFetcher != null) {
            try {
                legacyFetcher.close();
            } catch (Exception e) {
                logger.error("Error closing legacy fetcher", e);
            }
        }

        if (newFetcher != null) {
            try {
                newFetcher.close();
            } catch (Exception e) {
                logger.error("Error closing new fetcher", e);
            }
        }
    }

    @Test
    @Order(1)
    @DisplayName("基本页面抓取兼容性测试")
    void testBasicPageFetchingCompatibility() throws Exception {
        String url = baseUrl + "/simple";

        // 使用旧版本抓取
        PageData legacyResult = legacyFetcher.fetchPageContent(url);

        // 使用新版本抓取
        PageData newResult = newFetcher.fetchPageContent(url);

        // 验证基本属性
        assertNotNull(legacyResult, "旧版本应该返回结果");
        assertNotNull(newResult, "新版本应该返回结果");

        assertEquals(legacyResult.getUrl(), newResult.getUrl(), "URL应该相同");
        assertEquals(legacyResult.getTitle(), newResult.getTitle(), "标题应该相同");
        assertEquals(legacyResult.getResponseCode(), newResult.getResponseCode(), "响应代码应该相同");

        // 验证内容不为空
        assertNotNull(legacyResult.getHtmlContent(), "旧版本内容不应为空");
        assertNotNull(newResult.getHtmlContent(), "新版本内容不应为空");

        // 验证内容包含预期的文本
        assertTrue(legacyResult.getHtmlContent().contains("简单页面"), "旧版本内容应包含标题");
        assertTrue(newResult.getHtmlContent().contains("简单页面"), "新版本内容应包含标题");

        logger.info("基本兼容性测试通过 - 旧版本标题: '{}', 新版本标题: '{}'",
                   legacyResult.getTitle(), newResult.getTitle());
    }

    @Test
    @Order(2)
    @DisplayName("复杂页面处理兼容性测试")
    void testComplexPageHandlingCompatibility() throws Exception {
        String url = baseUrl + "/complex";

        PageData legacyResult = legacyFetcher.fetchPageContent(url);
        PageData newResult = newFetcher.fetchPageContent(url);

        assertNotNull(legacyResult);
        assertNotNull(newResult);

        assertEquals(legacyResult.getTitle(), newResult.getTitle());
        assertEquals(legacyResult.getResponseCode(), newResult.getResponseCode());

        // 验证都能处理JavaScript内容
        assertTrue(legacyResult.getHtmlContent().contains("复杂页面"));
        assertTrue(newResult.getHtmlContent().contains("复杂页面"));

        logger.info("复杂页面兼容性测试通过");
    }

    @Test
    @Order(3)
    @DisplayName("错误页面处理兼容性测试")
    void testErrorPageHandlingCompatibility() throws Exception {
        String url = baseUrl + "/error";

        PageData legacyResult = legacyFetcher.fetchPageContent(url);
        PageData newResult = newFetcher.fetchPageContent(url);

        assertNotNull(legacyResult);
        assertNotNull(newResult);

        // 两个版本都应该能够处理错误状态
        // 注意：具体的错误处理可能略有不同，但都应该返回有效的PageData
        assertTrue(legacyResult.getResponseCode() >= 400 || legacyResult.getTitle().contains("错误"));
        assertTrue(newResult.getResponseCode() >= 400 || newResult.getTitle().contains("错误"));

        logger.info("错误处理兼容性测试通过 - 旧版本状态码: {}, 新版本状态码: {}",
                   legacyResult.getResponseCode(), newResult.getResponseCode());
    }

    @Test
    @Order(4)
    @DisplayName("超时处理兼容性测试")
    void testTimeoutHandlingCompatibility() throws Exception {
        // 设置较短的超时时间
        PlaywrightProperties shortTimeoutProps = new PlaywrightProperties();
        shortTimeoutProps.setBrowserType("chromium");
        shortTimeoutProps.setHeadless(true);
        shortTimeoutProps.setTimeout(1000); // 1秒超时
        shortTimeoutProps.setRetries(1);
        shortTimeoutProps.setPoolSize(1);

        DynamicPageFetcher shortTimeoutLegacy = new DynamicPageFetcher(
            shortTimeoutProps.getBrowserType(),
            shortTimeoutProps.isHeadless(),
            shortTimeoutProps.getTimeout(),
            shortTimeoutProps.getRetries(),
            shortTimeoutProps.getPoolSize()
        );

        // 手动初始化DynamicPageFetcher
        try {
            java.lang.reflect.Method initMethod = DynamicPageFetcher.class.getDeclaredMethod("initializeBrowserPool");
            initMethod.setAccessible(true);
            initMethod.invoke(shortTimeoutLegacy);
        } catch (Exception e) {
            throw new RuntimeException("Failed to initialize short timeout legacy fetcher", e);
        }

        DynamicPageFetcherV2 shortTimeoutNew = new DynamicPageFetcherV2(shortTimeoutProps, performanceMonitor);
        shortTimeoutNew.initialize();

        try {
            String slowUrl = baseUrl + "/slow";

            // 测试超时处理
            PageData legacyResult = shortTimeoutLegacy.fetchPageContent(slowUrl);
            PageData newResult = shortTimeoutNew.fetchPageContent(slowUrl);

            // 两个版本都应该能处理超时情况，可能返回错误页面或抛出异常
            // 这里主要验证不会崩溃
            assertNotNull(legacyResult, "旧版本超时处理不应返回null");
            assertNotNull(newResult, "新版本超时处理不应返回null");

            logger.info("超时处理兼容性测试通过");

        } finally {
            shortTimeoutLegacy.close();
            shortTimeoutNew.close();
        }
    }

    @Test
    @Order(5)
    @DisplayName("资源清理兼容性测试")
    void testResourceCleanupCompatibility() throws Exception {
        // 创建临时的fetcher实例来测试资源清理
        DynamicPageFetcher tempLegacy = new DynamicPageFetcher(
            properties.getBrowserType(),
            properties.isHeadless(),
            properties.getTimeout(),
            properties.getRetries(),
            properties.getPoolSize()
        );

        // 手动初始化DynamicPageFetcher
        try {
            java.lang.reflect.Method initMethod = DynamicPageFetcher.class.getDeclaredMethod("initializeBrowserPool");
            initMethod.setAccessible(true);
            initMethod.invoke(tempLegacy);
        } catch (Exception e) {
            throw new RuntimeException("Failed to initialize temp legacy fetcher", e);
        }

        DynamicPageFetcherV2 tempNew = new DynamicPageFetcherV2(properties, performanceMonitor);
        tempNew.initialize();

        // 执行一些操作
        String url = baseUrl + "/simple";
        tempLegacy.fetchPageContent(url);
        tempNew.fetchPageContent(url);

        // 测试关闭操作不会抛出异常
        assertDoesNotThrow(() -> tempLegacy.close(), "旧版本关闭不应抛出异常");
        assertDoesNotThrow(() -> tempNew.close(), "新版本关闭不应抛出异常");

        logger.info("资源清理兼容性测试通过");
    }

    @Test
    @Order(6)
    @DisplayName("并发处理兼容性测试")
    void testConcurrentHandlingCompatibility() throws Exception {
        String url = baseUrl + "/simple";
        int concurrentRequests = 3;

        // 测试并发请求处理
        Thread[] legacyThreads = new Thread[concurrentRequests];
        Thread[] newThreads = new Thread[concurrentRequests];

        PageData[] legacyResults = new PageData[concurrentRequests];
        PageData[] newResults = new PageData[concurrentRequests];

        // 启动并发请求
        for (int i = 0; i < concurrentRequests; i++) {
            final int index = i;

            legacyThreads[i] = new Thread(() -> {
                try {
                    legacyResults[index] = legacyFetcher.fetchPageContent(url);
                } catch (Exception e) {
                    logger.error("Legacy concurrent request {} failed", index, e);
                }
            });

            newThreads[i] = new Thread(() -> {
                try {
                    newResults[index] = newFetcher.fetchPageContent(url);
                } catch (Exception e) {
                    logger.error("New concurrent request {} failed", index, e);
                }
            });
        }

        // 启动所有线程
        for (int i = 0; i < concurrentRequests; i++) {
            legacyThreads[i].start();
            newThreads[i].start();
        }

        // 等待所有线程完成
        for (int i = 0; i < concurrentRequests; i++) {
            legacyThreads[i].join(10000); // 最多等待10秒
            newThreads[i].join(10000);
        }

        // 验证结果 - 允许并发失败，但两个版本的行为应该一致
        int legacySuccessCount = 0;
        int newSuccessCount = 0;

        for (int i = 0; i < concurrentRequests; i++) {
            assertNotNull(legacyResults[i], "旧版本并发请求 " + i + " 应该有结果");
            assertNotNull(newResults[i], "新版本并发请求 " + i + " 应该有结果");

            // 统计成功的请求数量
            if (legacyResults[i].getResponseCode() == 200) {
                legacySuccessCount++;
            }
            if (newResults[i].getResponseCode() == 200) {
                newSuccessCount++;
            }

            // 如果两个版本都成功，标题应该相同
            if (legacyResults[i].getResponseCode() == 200 && newResults[i].getResponseCode() == 200) {
                assertEquals(legacyResults[i].getTitle(), newResults[i].getTitle(),
                            "并发请求 " + i + " 的标题应该相同");
            }
            // 如果两个版本都失败，状态码应该相同
            else if (legacyResults[i].getResponseCode() != 200 && newResults[i].getResponseCode() != 200) {
                // 允许不同的错误状态码，因为内部实现可能不同
                logger.debug("并发请求 {} 两个版本都失败: legacy={}, new={}",
                           i, legacyResults[i].getResponseCode(), newResults[i].getResponseCode());
            }
        }

        // 至少应该有一些成功的请求
        assertTrue(legacySuccessCount > 0 || newSuccessCount > 0,
                  "至少应该有一些并发请求成功");

        logger.info("并发处理兼容性测试通过 - 旧版本成功: {}, 新版本成功: {}",
                   legacySuccessCount, newSuccessCount);

        logger.info("并发处理兼容性测试通过");
    }

    @Test
    @Order(7)
    @DisplayName("接口兼容性测试")
    void testInterfaceCompatibility() {
        // 验证两个类都实现了IFetcher接口
        assertTrue(legacyFetcher instanceof IFetcher, "旧版本应该实现IFetcher接口");
        assertTrue(newFetcher instanceof IFetcher, "新版本应该实现IFetcher接口");

        // 验证可以通过接口调用
        IFetcher legacyInterface = legacyFetcher;
        IFetcher newInterface = newFetcher;

        assertNotNull(legacyInterface, "旧版本接口引用不应为空");
        assertNotNull(newInterface, "新版本接口引用不应为空");

        logger.info("接口兼容性测试通过");
    }
}
