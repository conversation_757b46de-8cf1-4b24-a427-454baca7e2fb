package com.talkweb.ai.crawler.integration;

import com.talkweb.ai.crawler.config.FetcherConfig;
import com.talkweb.ai.crawler.config.NetworkProperties;
import com.talkweb.ai.crawler.config.PlaywrightProperties;
import com.talkweb.ai.crawler.fetcher.DynamicPageFetcherV2;
import com.talkweb.ai.crawler.fetcher.IFetcher;
import com.talkweb.ai.crawler.model.PageData;
import com.talkweb.ai.crawler.monitor.PerformanceMonitor;
import com.talkweb.ai.crawler.testutils.MockHttpServer;
import org.junit.jupiter.api.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DynamicPageFetcherV2 Spring Boot集成测试
 * 验证新版本在Spring容器中的正确工作
 */
@SpringBootTest(classes = {
    FetcherConfig.class,
    NetworkProperties.class,
    PlaywrightProperties.class,
    PerformanceMonitor.class
})
@TestPropertySource(properties = {
    "crawler.enable-dynamic-fetcher=true",
    "crawler.use-new-dynamic-fetcher=true",
    "playwright.browser-type=chromium",
    "playwright.headless=true",
    "playwright.timeout=10000",
    "playwright.retries=2",
    "playwright.pool-size=2",
    "playwright.viewport-width=1280",
    "playwright.viewport-height=720"
})
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Disabled("Playwright integration tests are slow - browser startup takes too long in CI")
class DynamicPageFetcherV2IntegrationTest {

    private static final Logger logger = LoggerFactory.getLogger(DynamicPageFetcherV2IntegrationTest.class);

    private static MockHttpServer mockServer;
    private static String baseUrl;

    @Autowired
    private IFetcher fetcher; // 主要的Fetcher Bean

    @Autowired
    @Qualifier("dynamicPageFetcherV2")
    private DynamicPageFetcherV2 dynamicPageFetcherV2; // 显式的V2 Bean

    @Autowired
    private PlaywrightProperties playwrightProperties;

    @Autowired
    private PerformanceMonitor performanceMonitor;

    @BeforeAll
    static void setUpClass() throws Exception {
        mockServer = new MockHttpServer(8083); // 指定端口
        mockServer.start();
        baseUrl = mockServer.getBaseUrl();

        // 配置测试页面
        mockServer.addHtmlResponse("/test", "Spring集成测试",
            "Spring Boot集成测试页面内容");

        mockServer.addHtmlResponse("/health", "健康检查",
            "系统健康状态正常");

        logger.info("Mock server started for integration test at: {}", baseUrl);
    }

    @AfterAll
    static void tearDownClass() {
        if (mockServer != null) {
            mockServer.stop();
        }
    }

    @Test
    @Order(1)
    @DisplayName("Spring容器中的Bean注入测试")
    void testSpringBeanInjection() {
        // 验证所有必要的Bean都被正确注入
        assertNotNull(fetcher, "主要的IFetcher Bean应该被注入");
        assertNotNull(dynamicPageFetcherV2, "显式的DynamicPageFetcherV2 Bean应该被注入");
        assertNotNull(playwrightProperties, "PlaywrightProperties应该被注入");
        assertNotNull(performanceMonitor, "PerformanceMonitor应该被注入");

        // 验证主要的fetcher确实是DynamicPageFetcherV2实例
        assertTrue(fetcher instanceof DynamicPageFetcherV2,
                  "根据配置，主要的fetcher应该是DynamicPageFetcherV2实例");

        logger.info("Spring Bean注入测试通过");
    }

    @Test
    @Order(2)
    @DisplayName("配置属性注入测试")
    void testConfigurationProperties() {
        // 验证配置属性被正确加载
        assertEquals("chromium", playwrightProperties.getBrowserType());
        assertTrue(playwrightProperties.isHeadless());
        assertEquals(30000, playwrightProperties.getTimeout());
        assertEquals(3, playwrightProperties.getRetries());
        assertEquals(3, playwrightProperties.getPoolSize());
        assertEquals(1920, playwrightProperties.getViewportWidth());
        assertEquals(1080, playwrightProperties.getViewportHeight());

        logger.info("配置属性注入测试通过");
    }

    @Test
    @Order(3)
    @DisplayName("Spring容器中的基本功能测试")
    void testBasicFunctionalityInSpringContainer() throws Exception {
        String url = baseUrl + "/test";

        // 通过主要的IFetcher接口调用
        PageData result = fetcher.fetchPageContent(url);

        assertNotNull(result, "应该返回PageData结果");
        assertEquals(url, result.getUrl(), "URL应该匹配");
        assertEquals("Spring集成测试", result.getTitle(), "标题应该匹配");
        assertEquals(200, result.getResponseCode(), "响应代码应该是200");
        assertTrue(result.getHtmlContent().contains("Spring Boot集成测试页面"),
                  "内容应该包含预期文本");

        logger.info("Spring容器中的基本功能测试通过 - 标题: '{}'", result.getTitle());
    }

    @Test
    @Order(4)
    @DisplayName("显式Bean调用测试")
    void testExplicitBeanCall() throws Exception {
        String url = baseUrl + "/health";

        // 直接通过显式的DynamicPageFetcherV2 Bean调用
        PageData result = dynamicPageFetcherV2.fetchPageContent(url);

        assertNotNull(result);
        assertEquals("健康检查", result.getTitle());
        assertEquals(200, result.getResponseCode());

        logger.info("显式Bean调用测试通过");
    }

    @Test
    @Order(5)
    @DisplayName("健康检查功能测试")
    void testHealthCheckFunctionality() {
        // 测试DynamicPageFetcherV2特有的健康检查功能
        assertTrue(dynamicPageFetcherV2.isHealthy(), "DynamicPageFetcherV2应该是健康的");

        String statusReport = dynamicPageFetcherV2.getStatusReport();
        assertNotNull(statusReport, "状态报告不应为空");
        assertTrue(statusReport.contains("DynamicPageFetcherV2"), "状态报告应包含类名");
        assertTrue(statusReport.contains("Initialized: true"), "状态报告应显示已初始化");

        logger.info("健康检查功能测试通过");
        logger.debug("状态报告:\n{}", statusReport);
    }

    @Test
    @Order(6)
    @DisplayName("浏览器池状态测试")
    void testBrowserPoolStatus() {
        var poolStatus = dynamicPageFetcherV2.getBrowserPoolStatus();

        assertNotNull(poolStatus, "浏览器池状态不应为空");
        assertEquals(3, poolStatus.getTotalBrowsers(), "总池大小应该是2");
        assertTrue(poolStatus.getAvailableBrowsers() >= 0, "可用数量应该>=0");
        assertTrue(poolStatus.getActiveBrowsers() >= 0, "活跃数量应该>=0");
        assertFalse(poolStatus.isShutdown(), "池不应该被关闭");

        logger.info("浏览器池状态测试通过 - 总大小: {}, 可用: {}, 活跃: {}",
                   poolStatus.getTotalBrowsers(), poolStatus.getAvailableBrowsers(), poolStatus.getActiveBrowsers());
    }

    @Test
    @Order(7)
    @DisplayName("重试统计功能测试")
    void testRetryStatistics() throws Exception {
        // 重置统计信息
        dynamicPageFetcherV2.resetStatistics();

        var initialStats = dynamicPageFetcherV2.getRetryStatistics();
        assertNotNull(initialStats, "重试统计不应为空");

        // 执行一次成功的请求
        String url = baseUrl + "/test";
        dynamicPageFetcherV2.fetchPageContent(url);

        var afterStats = dynamicPageFetcherV2.getRetryStatistics();
        assertTrue(afterStats.getTotalOperations() >= initialStats.getTotalOperations(),
                  "总尝试次数应该增加");

        logger.info("重试统计功能测试通过 - 总操作: {}, 成功: {}, 失败: {}",
                   afterStats.getTotalOperations(), afterStats.getSuccessfulOperations(), afterStats.getFailedOperations());
    }

    @Test
    @Order(8)
    @DisplayName("异步功能测试")
    void testAsyncFunctionality() throws Exception {
        String url = baseUrl + "/test";

        // 测试异步抓取
        var future = dynamicPageFetcherV2.fetchPageContentAsync(url);
        assertNotNull(future, "异步Future不应为空");

        PageData result = future.get(); // 等待完成
        assertNotNull(result, "异步结果不应为空");
        assertEquals("Spring集成测试", result.getTitle(), "异步结果标题应该正确");

        logger.info("异步功能测试通过 - 异步获取标题: '{}'", result.getTitle());
    }

    @Test
    @Order(9)
    @DisplayName("Spring生命周期集成测试")
    void testSpringLifecycleIntegration() {
        // 验证Spring的@PostConstruct已经被调用
        assertTrue(dynamicPageFetcherV2.isHealthy(),
                  "@PostConstruct应该已经初始化了组件");

        // 验证状态报告显示已初始化
        String status = dynamicPageFetcherV2.getStatusReport();
        assertTrue(status.contains("Initialized: true"),
                  "Spring生命周期应该正确初始化组件");

        logger.info("Spring生命周期集成测试通过");
    }
}
