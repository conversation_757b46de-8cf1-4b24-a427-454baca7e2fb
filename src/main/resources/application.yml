# ===================================================================
# Default Application Properties
# ===================================================================

spring:
  # 启动性能优化配置
  main:
    lazy-initialization: false  # 全局延迟加载在生产环境谨慎使用
    banner-mode: console

  # JPA 启动优化
  jpa:
    hibernate:
      ddl-auto: update
    defer-datasource-initialization: false  # 避免启动延迟
    show-sql: false  # 减少日志噪音

  # 数据源配置
  datasource:
    url: jdbc:h2:mem:testdb
    driverClassName: org.h2.Driver
    username: sa
    password: ''

  h2:
    console:
      enabled: true

  # 缓存快速启动配置
  cache:
    type: simple  # 开发环境使用简单缓存快速启动

  # Spring Security 配置优化
  security:
    user:
      name: admin
      password: admin123
      roles: ADMIN

# Spring Boot Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,env
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
    prometheus:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
        step: 15s
        descriptions: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5,0.75,0.90,0.95,0.99
      sla:
        http.server.requests: 100ms,500ms,1s,5s

crawler:
  cli:
    enabled: false
  url: ""
  max-depth: 3
  allowed-domains: []
  threads: 4
  enable-dynamic-fetcher: false
  download-attachments: false
  external-link-max-depth: 1
  exit-on-complete: true
  deep-detect-html: false

  # 核心配置
  core:
    enable-url-validation-cache: true
    url-validation-cache-minutes: 30
    crawl-task-timeout-seconds: 300

  # 智能内容分析和策略优化配置
  intelligent:
    content-analysis:
      enabled: true
      quality-threshold: 0.7
      extract-keywords: true
      max-keywords: 20
      detect-language: true
      duplicate-threshold: 0.85
      generate-summary: true
      max-summary-sentences: 3

    strategy:
      enabled: true  # 启用智能策略演示
      dynamic-scheduling: true
      adaptive-concurrency: true
      priority-based: true
      max-priority-score: 100.0

    performance:
      async-analysis: true
      batch-size: 50
      cache-enabled: true
      cache-ttl: 3600  # 秒

  # 事件驱动架构配置
  events:
    core-pool-size: 4
    max-pool-size: 16
    queue-capacity: 100
    thread-name-prefix: "event-handler-"
    async-enabled: true
    timeout-ms: 30000
    metrics-reporting-interval-seconds: 60
    monitoring:
      enabled: true
      log-event-details: false
      metrics-reporting-interval-seconds: 60

  # 多级缓存配置
  cache:
    multi-level:
      l1:
        enabled: true
        max-size: 1000
        ttl: PT10M
        max-memory-mb: 64
        enable-expiration: true
        expiration-check-interval: PT1M
      l2:
        enabled: true
        max-size: 10000
        ttl: PT1H
        store-path: "./cache/l2"
        max-disk-mb: 256
        enable-compression: true
        serialization-format: "json"
      l3:
        enabled: false
        max-size: 100000
        ttl: PT24H
        redis-connection-string: "redis://localhost:6379"
        redis-database: 0
        connection-timeout: PT5S
        key-prefix: "crawler:cache:"
        enable-cluster: false
      general:
        enable-statistics: true
        enable-warmup: false
        warmup-size: 100
        enable-auto-cleanup: true
        auto-cleanup-interval: PT30M
        enable-performance-monitoring: true
        performance-report-interval: PT5M
        hit-rate-warning-threshold: 0.5
        enable-cache-prediction: false
        prediction-algorithm: "lru"

  # 数据库优化配置
  database:
    optimization:
      enabled: true
      analysis-interval: 3600000  # 1小时分析一次
      slow-query-threshold-ms: 1000
      auto-apply-safe-recommendations: true
      max-auto-recommendations: 3
      enable-query-monitoring: true
      enable-index-analysis: true
      performance-report-interval: 1800000  # 30分钟报告一次

  # Prometheus监控配置
  prometheus:
    environment: "development"
    instance-id: "crawler-001"
    enable-jvm-metrics: true
    enable-system-metrics: true
    enable-custom-metrics: true
    max-expected-duration-seconds: 30
    denied-metrics: []
    accepted-metrics: []
    metrics-export-interval-seconds: 15
    enable-detailed-timers: false
    enable-memory-metrics: true
    enable-disk-metrics: true
    enable-network-metrics: false

  retry:
    default-max-retries: 3
    default-base-delay-ms: 1000
    default-backoff-multiplier: 2.0
    default-max-delay-ms: 30000
    circuit-breaker-failure-threshold: 10
    circuit-breaker-timeout-minutes: 10
    max-retries-per-error:
      NETWORK_TIMEOUT: 3
      CONNECTION_REFUSED: 3
      TEMPORARY_SERVER_ERROR: 3
      DNS_RESOLUTION_FAILED: 2
      SSL_HANDSHAKE_FAILED: 2
      NOT_FOUND: 0
      FORBIDDEN: 0
      UNAUTHORIZED: 0
      PERMANENT_SERVER_ERROR: 0
      RATE_LIMITED: 5
      PARSING_ERROR: 1

  # 监控和可观测性配置
  observability:
    enabled: true
  monitoring:
    enabled: true
    metrics:
      collection-interval: 10s
      cache-ttl: 5s
      business-metrics: true
      performance-metrics: true
      health-metrics: true
    prometheus:
      enabled: true
      custom-metrics: true
      business-events: true
      histogram-buckets:
        fetch-duration: [0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0, 60.0]
        content-size: [1000, 10000, 100000, 1000000, 10000000]

  # 审计日志配置
  audit:
    enabled: true
    retention-days: 90
    async-logging: true
    async-thread-pool-size: 5
    log-success-operations: true
    log-failure-operations: true
    log-system-operations: true
    log-api-access: true
    excluded-user-ids: ["health-check", "metrics", "system"]
    high-risk-alert:
      enabled: true
      failure-threshold: 5
      time-window-minutes: 15
      email-recipients: []
    sensitive-data-masking:
      enabled: true
      masked-fields: ["password", "token", "secret", "key"]
      mask-character: "*"
      prefix-length: 2
      suffix-length: 2
    data-export:
      enabled: true
      supported-formats: ["CSV", "JSON", "XLSX"]
      max-records-per-export: 10000
      export-path: "./exports/audit"
      export-retention-days: 7

  # SLA监控和报警配置
  sla:
    enabled: true
    monitoring-interval-seconds: 60
    default-violation-tolerance-percent: 5.0
    default-time-window-minutes: 5
    long-running-violation-hours: 4
    history-retention-days: 90
    alerting:
      enabled: true
      default-channels: "email"
      webhook-url: ""
      suppression-minutes: 15
      escalation-threshold-minutes: 30
      email:
        enabled: true
        from-address: "<EMAIL>"
        to-addresses: "<EMAIL>"
        subject: "SLA违规告警"
      slack:
        enabled: false
        webhook-url: ""
        channel: "#alerts"
        username: "SLA Monitor"
    monitoring:
      metric-collection-interval-seconds: 30
      batch-size: 100
      executor:
        core-pool-size: 2
        max-pool-size: 8
        queue-capacity: 100
        thread-name-prefix: "sla-monitor-"
        keep-alive-time: "PT1M"
      cache:
        enabled: true
        max-size: 1000
        expire-after-write: "PT5M"
        expire-after-access: "PT10M"

  # 数据备份和恢复配置
  backup:
    enabled: true
    backup-directory: "./backups"
    default-retention-days: 30
    max-concurrent-backups: 2
    backup-timeout-minutes: 60
    auto-cleanup-enabled: true
    cleanup-cron: "0 0 2 * * ?"
    default-compression-type: "GZIP"
    encryption-enabled: false
    notification:
      enabled: true
      notify-on-success: false
      notify-on-failure: true
      long-running-threshold-minutes: 30
      notify-on-long-running: true
      email:
        enabled: false
        from-address: "<EMAIL>"
        to-addresses: []
        subject: "备份作业通知"
      webhook:
        enabled: false
        url: ""
        timeout-seconds: 10
    scheduling:
      enabled: true
      default-schedules:
        full-database-daily: "0 0 1 * * ?"
        incremental-hourly: "0 0 * * * ?"
        audit-logs-daily: "0 30 1 * * ?"
        configuration-weekly: "0 0 2 * * SUN"
      executor:
        core-pool-size: 2
        max-pool-size: 5
        queue-capacity: 50
        thread-name-prefix: "backup-"
        keep-alive-time: "PT5M"
    storage:
      max-backup-file-size-mb: 1024
      max-total-storage-gb: 50
      storage-warning-threshold-percent: 80
      storage-monitoring-enabled: true
      sharding:
        enabled: false
        shard-size-mb: 100
        max-shards-per-backup: 50
    recovery:
      enabled: true
      require-confirmation: true
      create-pre-recovery-backup: true
      recovery-timeout-minutes: 120
      validation:
        enabled: true
        validate-data-integrity: true
        validate-foreign-keys: true
        validate-constraints: true
    monitoring:
      enabled: true
      metrics-collection-interval-seconds: 60
      health-check-interval-seconds: 30
      collect-performance-metrics: true
      collect-storage-metrics: true
      alert:
        enabled: true
        failure-threshold: 3
        long-running-threshold-minutes: 45
        storage-usage-threshold-percent: 85

  # 分布式追踪配置
  tracing:
    enabled: true
    service-name: "web-crawler"
    sampling-rate: 1.0
    max-active-spans: 10000
    span-timeout-minutes: 30
    auto-inject-http-headers: true
    http-trace-header: "X-Trace-Context"
    data-retention:
      retention-days: 7
      auto-cleanup-enabled: true
      cleanup-cron: "0 0 3 * * ?"
      batch-delete-size: 1000
      compress-old-data: false
      compression-threshold-days: 1
    monitoring:
      enabled: true
      long-running-threshold-minutes: 10
      slow-operation-threshold-micros: 1000000
      error-rate-threshold: 5.0
      metrics-collection-interval-seconds: 60
      alert:
        enabled: true
        webhook-url: ""
        email-recipients: ""
        error-count-threshold: 10
        time-window-minutes: 5
    storage:
      async-storage: true
      batch-size: 100
      batch-interval-ms: 1000
      queue-capacity: 10000
      storage-timeout-seconds: 5
      max-tags-length: 5000
      max-logs-length: 10000
    export:
      enabled: false
      format: "JSON"
      endpoint-url: ""
      export-interval-seconds: 10
      batch-size: 50
      timeout-seconds: 30
      max-retries: 3
      jaeger:
        enabled: false
        collector-url: "http://localhost:14268/api/traces"
        agent-host: "localhost"
        agent-port: 6831
      zipkin:
        enabled: false
        collector-url: "http://localhost:9411/api/v2/spans"
    performance:
      thread-pool:
        core-pool-size: 2
        max-pool-size: 8
        queue-capacity: 1000
        thread-name-prefix: "tracing-"
        keep-alive-time: "PT2M"
      cache:
        enabled: true
        max-size: 5000
        expire-after-write: "PT10M"
        expire-after-access: "PT5M"

  # 自定义指标配置
  metrics:
    custom:
      enabled: true
      default-sampling-rate: 1.0
      batch-size: 100
      batch-interval-ms: 5000
      max-cache-size: 10000
      cache-expiration-minutes: 10
      data-retention:
        raw-data-retention-days: 7
        aggregated-data-retention-days: 30
        auto-cleanup-enabled: true
        cleanup-cron: "0 0 4 * * ?"
        compress-old-data: false
        compression-threshold-days: 1
      alerting:
        enabled: true
        suppression-minutes: 15
        anomaly-detection-threshold: 25.0
        email:
          enabled: false
          from-address: "<EMAIL>"

  # 性能基线配置
  baseline:
    enabled: true
    validity-days: 30
    auto-establish: false
    auto-establish-components: []
    regression-threshold: 10.0
    data-retention:
      retention-days: 90
      archive-threshold-days: 60
      auto-cleanup: true
    thresholds:
      default-warning-threshold: 20.0
      default-critical-threshold: 50.0
      trend-stability-threshold: 5.0
    reporting:
      daily-report: true
      weekly-report: true
      email-recipients: []
      dashboard-refresh-interval: 300

  # 健康检查配置
  health:
    enabled: true
    network-timeout: 5000
    check-external-urls: true
    monitoring:
      enabled: true
      interval-seconds: 60
      history-size: 100
      alert-threshold: 70
      critical-threshold: 50
      recovery-attempts: 3
      aggregation:
        enabled: true
        aggregation-interval-minutes: 5
        aggregation-functions: ["AVG", "MAX", "MIN", "SUM", "COUNT"]
        precompute-aggregations: true
        windows:
          hourly: true
          daily: true
          weekly: false
          monthly: false
      export:
        enabled: false
        formats: ["JSON", "CSV"]
        export-interval-minutes: 15
        export-directory: "./exports/metrics"
        file-retention-days: 7
        prometheus:
          enabled: true
          endpoint: "/metrics/custom"
          include-labels: true
          prefix: "custom_"
        influxdb:
          enabled: false
          url: "http://localhost:8086"
          database: "crawler_metrics"
          measurement: "custom_metrics"
      performance:
        async-processing: true
        thread-pool:
          core-pool-size: 2
          max-pool-size: 10
          queue-capacity: 1000
          thread-name-prefix: "metrics-"
          keep-alive-time: "PT2M"
        memory:
          max-memory-usage-mb: 512
          enable-memory-monitoring: true
          memory-check-interval-seconds: 60

network:
  connection-timeout: 10000
  read-timeout: 10000
  max-connections-per-domain: 5
  max-retries: 3
  user-agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"

storage:
  output-dir: "./output"
  max-file-size: 10485760 # 10 MB

playwright:
  browser-type: "chromium"
  headless: true
  timeout: 30000

# Legacy prometheus configuration (deprecated)
prometheus:
  # 保留为空的配置块，避免使用已弃用的配置
