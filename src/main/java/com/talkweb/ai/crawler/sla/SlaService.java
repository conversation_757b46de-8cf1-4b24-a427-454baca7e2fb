package com.talkweb.ai.crawler.sla;

import com.talkweb.ai.crawler.sla.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * SLA监控服务
 * 提供服务级别协议监控、违规检测、报警和报告功能
 */
@Slf4j
@Service
@Transactional
public class SlaService {

    @Autowired
    private SlaDefinitionRepository slaDefinitionRepository;

    @Autowired
    private SlaViolationRepository slaViolationRepository;

    @Autowired
    private SlaConfigurationProperties slaConfig;

    // SLA指标缓存
    private final Map<String, SlaMetric> metricsCache = new HashMap<>();

    // 违规状态缓存
    private final Map<String, LocalDateTime> violationStatusCache = new HashMap<>();

    /**
     * 创建SLA定义
     */
    public SlaDefinition createSlaDefinition(SlaDefinition slaDefinition) {
        slaDefinition.setCreatedAt(LocalDateTime.now());
        slaDefinition.setUpdatedAt(LocalDateTime.now());
        slaDefinition.setStatus(SlaDefinition.SlaStatus.ACTIVE);

        SlaDefinition saved = slaDefinitionRepository.save(slaDefinition);
        log.info("创建SLA定义: {} - {}", saved.getServiceName(), saved.getMetricName());

        return saved;
    }

    /**
     * 更新SLA定义
     */
    public SlaDefinition updateSlaDefinition(UUID slaId, SlaDefinition slaDefinition) {
        Optional<SlaDefinition> existingOpt = slaDefinitionRepository.findById(slaId);
        if (existingOpt.isEmpty()) {
            throw new IllegalArgumentException("SLA定义不存在: " + slaId);
        }

        SlaDefinition existing = existingOpt.get();
        existing.setMetricName(slaDefinition.getMetricName());
        existing.setThresholdValue(slaDefinition.getThresholdValue());
        existing.setThresholdType(slaDefinition.getThresholdType());
        existing.setTimeWindow(slaDefinition.getTimeWindow());
        existing.setTimeUnit(slaDefinition.getTimeUnit());
        existing.setDescription(slaDefinition.getDescription());
        existing.setUpdatedAt(LocalDateTime.now());

        return slaDefinitionRepository.save(existing);
    }

    /**
     * 检查SLA违规
     */
    @Async("slaExecutor")
    public CompletableFuture<Void> checkSlaViolation(String serviceName, String metricName,
                                                    double currentValue, String unit) {
        try {
            List<SlaDefinition> slaDefinitions = slaDefinitionRepository.findByServiceNameAndMetricNameAndEnabled(serviceName, metricName,true);

            for (SlaDefinition sla : slaDefinitions) {
                boolean isViolation = isViolation(sla, currentValue);
                if (isViolation) {
                    handleSlaViolation(sla, currentValue, unit);
                } else {
                    handleSlaCompliance(sla, currentValue, unit);
                }
            }

        } catch (Exception e) {
            log.error("检查SLA违规失败: service={}, metric={}, value={}",
                serviceName, metricName, currentValue, e);
        }

        return CompletableFuture.completedFuture(null);
    }

    /**
     * 批量检查SLA违规
     */
    @Async("slaExecutor")
    public CompletableFuture<Void> batchCheckSlaViolations(List<SlaMetric> metrics) {
        for (SlaMetric metric : metrics) {
            checkSlaViolation(metric.getServiceName(), metric.getMetricName(),
                            metric.getValue(), metric.getUnit());
        }

        return CompletableFuture.completedFuture(null);
    }

    /**
     * 处理SLA违规
     */
    private void handleSlaViolation(SlaDefinition sla, double currentValue, String unit) {
        String violationKey = sla.getViolationKey();
        LocalDateTime lastViolation = violationStatusCache.get(violationKey);

        // 防止重复记录短时间内的违规
        if (lastViolation != null && lastViolation.isAfter(LocalDateTime.now().minusMinutes(
            slaConfig.getAlerting().getSuppressionMinutes()))) {
            return;
        }

        SlaDefinition.ViolationLevel violationLevel = sla.getViolationLevel(currentValue);

        SlaViolation violation = SlaViolation.builder()
            .slaDefinitionId(sla.getId())
            .serviceName(sla.getServiceName())
            .metricName(sla.getMetricName())
            .targetValue(sla.getThresholdValue())
            .actualValue(currentValue)
            .violationLevel(violationLevel)
            .violationTime(LocalDateTime.now())
            .status(SlaViolation.ViolationStatus.ACTIVE)
            .description(generateViolationDescription(sla, currentValue, unit))
            .build();

        slaViolationRepository.save(violation);

        // 发送告警
        sendSlaAlert(violation);

        // 更新缓存
        violationStatusCache.put(violationKey, LocalDateTime.now());

        log.warn("SLA违规: {} - {} = {} {} (期望: {} {})",
            sla.getServiceName(), sla.getMetricName(),
            currentValue, unit, sla.getThresholdValue(), unit);
    }

    /**
     * 处理SLA合规
     */
    private void handleSlaCompliance(SlaDefinition sla, double currentValue, String unit) {
        String violationKey = sla.getViolationKey();

        // 检查是否有活跃的违规需要恢复
        List<SlaViolation> activeViolations = slaViolationRepository
            .findBySlaDefinitionIdAndStatus(sla.getId(), SlaViolation.ViolationStatus.ACTIVE);

        for (SlaViolation violation : activeViolations) {
            violation.setStatus(SlaViolation.ViolationStatus.RESOLVED);
            violation.setResolvedTime(LocalDateTime.now());
            violation.setResolutionDescription("指标恢复正常");

            slaViolationRepository.save(violation);

            log.info("SLA违规恢复: {} - {} = {} {}",
                sla.getServiceName(), sla.getMetricName(), currentValue, unit);
        }

        // 清除违规状态
        violationStatusCache.remove(violationKey);
    }

    /**
     * 评估SLA违规
     */
    private boolean isViolation(SlaDefinition sla, double currentValue) {
        switch (sla.getThresholdType()) {
            case GREATER_THAN:
                return currentValue > sla.getThresholdValue();
            case LOWER_THAN:
                return currentValue < sla.getThresholdValue();
            case EQUALS:
                return currentValue == sla.getThresholdValue();
            default:
                return false;
        }
    }

    /**
     * 确定违规类型
     */
    private SlaViolation.ViolationType determineViolationType(SlaDefinition sla, double currentValue) {
        double deviationPercent = Math.abs((currentValue - sla.getThresholdValue()) / sla.getThresholdValue()) * 100;

        if (deviationPercent > 50) {
            return SlaViolation.ViolationType.CRITICAL;
        } else if (deviationPercent > 20) {
            return SlaViolation.ViolationType.MAJOR;
        } else if (deviationPercent > 10) {
            return SlaViolation.ViolationType.MINOR;
        } else {
            return SlaViolation.ViolationType.WARNING;
        }
    }

    /**
     * 确定严重程度
     */
    private SlaViolation.ViolationSeverity determineSeverity(SlaDefinition sla, double currentValue) {
        double deviationPercent = Math.abs((currentValue - sla.getThresholdValue()) / sla.getThresholdValue()) * 100;

        if (deviationPercent > 100) {
            return SlaViolation.ViolationSeverity.CRITICAL;
        } else if (deviationPercent > 50) {
            return SlaViolation.ViolationSeverity.HIGH;
        } else if (deviationPercent > 20) {
            return SlaViolation.ViolationSeverity.MEDIUM;
        } else {
            return SlaViolation.ViolationSeverity.LOW;
        }
    }

    /**
     * 生成违规描述
     */
    private String generateViolationDescription(SlaDefinition sla, double currentValue, String unit) {
        double deviationPercent = Math.abs((currentValue - sla.getThresholdValue()) / sla.getThresholdValue()) * 100;

        return String.format("SLA违规: %s指标 %s 当前值 %.2f %s 超过阈值 %.2f %s，偏离 %.1f%%",
            sla.getServiceName(), sla.getMetricName(),
            currentValue, unit, sla.getThresholdValue(), unit, deviationPercent);
    }

    /**
     * 发送SLA告警
     */
    private void sendSlaAlert(SlaViolation violation) {
        log.error("SLA告警: {} - {} - {} - 严重程度: {}",
            violation.getServiceName(), violation.getMetricName(),
            violation.getViolationType(), violation.getSeverity());

        // 这里可以集成邮件、短信、Webhook等告警通道
        if (violation.getSeverity() == SlaViolation.ViolationSeverity.CRITICAL) {
            // 发送紧急通知
            log.error("发送紧急SLA告警通知: {}", violation.getDescription());
        }
    }

    /**
     * 获取SLA合规性报告
     */
    public SlaComplianceReport getSlaComplianceReport(String serviceName,
                                                     LocalDateTime startTime, LocalDateTime endTime) {
        List<SlaDefinition> slaDefinitions = slaDefinitionRepository.findByServiceName(serviceName);

        SlaComplianceReport report = SlaComplianceReport.builder()
            .serviceName(serviceName)
            .reportPeriodStart(startTime)
            .reportPeriodEnd(endTime)
            .generatedAt(LocalDateTime.now())
            .build();

        List<SlaComplianceReport.MetricCompliance> metricCompliances = new ArrayList<>();
        double totalComplianceRate = 0.0;

        for (SlaDefinition sla : slaDefinitions) {
            SlaComplianceReport.MetricCompliance compliance = calculateMetricCompliance(sla, startTime, endTime);
            metricCompliances.add(compliance);
            totalComplianceRate += compliance.getComplianceRate();
        }

        report.setMetricCompliances(metricCompliances);
        report.setOverallComplianceRate(slaDefinitions.isEmpty() ? 100.0 : totalComplianceRate / slaDefinitions.size());

        return report;
    }

    /**
     * 计算指标合规性
     */
    private SlaComplianceReport.MetricCompliance calculateMetricCompliance(SlaDefinition sla,
                                                     LocalDateTime startTime,
                                                     LocalDateTime endTime) {
        List<SlaViolation> violations = slaViolationRepository
            .findBySlaDefinitionIdAndViolationTimeBetween(sla.getId(), startTime, endTime);

        long totalMinutes = java.time.Duration.between(startTime, endTime).toMinutes();
        long violationMinutes = violations.stream()
            .mapToLong(v -> {
                LocalDateTime vStart = v.getViolationTime().isAfter(startTime) ? v.getViolationTime() : startTime;
                LocalDateTime vEnd = (v.getResolvedTime() != null && v.getResolvedTime().isBefore(endTime))
                    ? v.getResolvedTime() : endTime;
                return java.time.Duration.between(vStart, vEnd).toMinutes();
            })
            .sum();

        double complianceRate = totalMinutes > 0 ?
            ((double)(totalMinutes - violationMinutes) / totalMinutes) * 100 : 100.0;

        return SlaComplianceReport.MetricCompliance.builder()
            .metricName(sla.getMetricName())
            .targetValue(sla.getThresholdValue())
            .complianceRate(complianceRate)
            .violationCount(violations.size())
            .totalViolationDurationMinutes(violationMinutes)
            .build();
    }

    /**
     * 获取SLA违规统计
     */
    public SlaViolationStatistics getSlaViolationStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        long totalViolations = slaViolationRepository.countByViolationTimeBetween(startTime, endTime);

        List<Object[]> serviceStats = slaViolationRepository.countViolationsByService(startTime, endTime);
        List<Object[]> typeStats = slaViolationRepository.countViolationsByType(startTime, endTime);
        List<Object[]> severityStats = slaViolationRepository.countViolationsBySeverity(startTime, endTime);

        List<SlaViolation> activeViolations = slaViolationRepository
            .findByStatusAndViolationTimeBetween(SlaViolation.ViolationStatus.ACTIVE, startTime, endTime);

        return SlaViolationStatistics.builder()
            .totalViolations( (int) totalViolations)
            .activeViolations(activeViolations.size())
            .serviceStatistics(convertToServiceStats(serviceStats))
            .typeStatistics(convertToTypeStats(typeStats))
            .severityStatistics(convertToSeverityStats(severityStats))
            .reportPeriodStart(startTime)
            .reportPeriodEnd(endTime)
            .generatedAt(LocalDateTime.now())
            .build();
    }

    /**
     * 获取服务SLA总览
     */
    public ServiceSlaOverview getServiceSlaOverview(String serviceName) {
        List<SlaDefinition> slaDefinitions = slaDefinitionRepository.findByServiceName(serviceName);
        List<SlaViolation> recentViolations = slaViolationRepository
            .findRecentViolationsByService(serviceName, PageRequest.of(0, 10));

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime last24Hours = now.minusHours(24);

        double overallCompliance = calculateOverallCompliance(serviceName, last24Hours, now);

        return ServiceSlaOverview.builder()
            .serviceName(serviceName)
            .totalSlaDefinitions(slaDefinitions.size())
            .activeSlaDefinitions(slaDefinitions.stream()
                .mapToInt(sla -> sla.getStatus() == SlaDefinition.SlaStatus.ACTIVE ? 1 : 0)
                .sum())
            .overallComplianceRate(overallCompliance)
            .recentViolations(recentViolations)
            .lastUpdated(now)
            .build();
    }

    /**
     * 计算整体合规率
     */
    private double calculateOverallCompliance(String serviceName, LocalDateTime startTime, LocalDateTime endTime) {
        List<SlaDefinition> slaDefinitions = slaDefinitionRepository.findByServiceName(serviceName);

        if (slaDefinitions.isEmpty()) {
            return 100.0;
        }

        double totalCompliance = 0.0;
        for (SlaDefinition sla : slaDefinitions) {
            SlaComplianceReport.MetricCompliance compliance = calculateMetricCompliance(sla, startTime, endTime);
            totalCompliance += compliance.getComplianceRate();
        }

        return totalCompliance / slaDefinitions.size();
    }

    /**
     * 定时检查SLA状态
     */
    @Scheduled(fixedRate = 60000) // 每分钟执行
    public void scheduledSlaCheck() {
        if (!slaConfig.isEnabled()) {
            return;
        }

        // 清理过期的SLA违规记录
        LocalDateTime expirationTime = LocalDateTime.now().minusDays(slaConfig.getHistoryRetentionDays());
        int deletedCount = slaViolationRepository.deleteExpiredViolations(expirationTime);
        if (deletedCount > 0) {
            log.info("清理了 {} 条过期的SLA违规记录", deletedCount);
        }

        try {
            // 这里可以集成实际的指标收集系统
            // 暂时使用模拟数据进行演示

            List<SlaDefinition> activeSlas = slaDefinitionRepository.findByEnabled(true);

            for (SlaDefinition sla : activeSlas) {
                // 模拟获取当前指标值
                double currentValue = simulateMetricValue(sla);
                checkSlaViolation(sla.getServiceName(), sla.getMetricName(), currentValue, "ms");
            }

        } catch (Exception e) {
            log.error("定时SLA检查失败", e);
        }
    }

    /**
     * 定时清理过期违规记录
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void cleanupExpiredViolations() {
        if (!slaConfig.isEnabled()) {
            return;
        }

        try {
            LocalDateTime cutoffDate = LocalDateTime.now()
                .minusDays(slaConfig.getHistoryRetentionDays());

            long deletedCount = slaViolationRepository.deleteExpiredViolations(cutoffDate);

            log.info("清理过期SLA违规记录完成，删除数量: {}, 截止时间: {}", deletedCount, cutoffDate);

        } catch (Exception e) {
            log.error("清理过期SLA违规记录失败", e);
        }
    }

    /**
     * 模拟指标值（用于演示）
     */
    private double simulateMetricValue(SlaDefinition sla) {
        Random random = new Random();
        double baseValue = sla.getThresholdValue();

        // 90%的时间返回正常值，10%的时间返回异常值
        if (random.nextDouble() < 0.9) {
            return baseValue * (0.8 + random.nextDouble() * 0.4); // 80%-120%范围
        } else {
            return baseValue * (1.5 + random.nextDouble() * 0.5); // 150%-200%范围（违规）
        }
    }

    // 辅助方法
    private List<ServiceStatistic> convertToServiceStats(List<Object[]> stats) {
        return stats.stream()
            .map(row -> ServiceStatistic.of(
                (String) row[0],
                ((Number) row[1]).longValue(),
                5L, // active violations
                ((Number) row[1]).longValue() - 5)) // resolved
            .collect(Collectors.toList());
    }

    private List<TypeStatistic> convertToTypeStats(List<Object[]> stats) {
        return stats.stream()
            .map(row -> TypeStatistic.of(
                (String) row[0],
                ((Number) row[1]).longValue(),
                1000L)) // total
            .collect(Collectors.toList());
    }

    private List<com.talkweb.ai.crawler.audit.model.SeverityStatistic> convertToSeverityStats(List<Object[]> stats) {
        return stats.stream()
            .map(row -> com.talkweb.ai.crawler.audit.model.SeverityStatistic.of(
                (String) row[0],
                ((Number) row[1]).longValue(),
                1000L)) // total
            .collect(Collectors.toList());
    }
}
