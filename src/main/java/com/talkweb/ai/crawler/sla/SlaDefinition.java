package com.talkweb.ai.crawler.sla;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * SLA定义实体
 * 定义服务级别协议的指标和阈值
 */
@Entity
@Table(name = "sla_definitions", indexes = {
    @Index(name = "idx_sla_service", columnList = "service_name"),
    @Index(name = "idx_sla_metric", columnList = "metric_name"),
    @Index(name = "idx_sla_enabled", columnList = "enabled")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SlaDefinition {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    /**
     * 服务名称
     */
    @Column(name = "service_name", nullable = false, length = 100)
    private String serviceName;

    /**
     * 指标名称
     */
    @Column(name = "metric_name", nullable = false, length = 100)
    private String metricName;

    /**
     * 阈值
     */
    @Column(name = "threshold_value", nullable = false)
    private Double thresholdValue;

    /**
     * 阈值类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "threshold_type", nullable = false, length = 30)
    private ThresholdType thresholdType;

    /**
     * 时间窗口
     */
    @Column(name = "time_window")
    private Integer timeWindow;

    /**
     * 时间单位
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "time_unit", length = 20)
    private TimeUnit timeUnit;

    /**
     * 描述
     */
    @Column(name = "description", length = 500)
    private String description;

    /**
     * 是否启用
     */
    @Column(name = "enabled", nullable = false)
    private Boolean enabled = true;

    /**
     * 严重程度
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "severity", length = 20)
    private Severity severity;

    /**
     * 通知渠道
     */
    @Column(name = "notification_channels", length = 200)
    private String notificationChannels; // JSON格式存储

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 创建者
     */
    @Column(name = "created_by", length = 50)
    private String createdBy;

    /**
     * SLA类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "sla_type", length = 30)
    private SlaType slaType;

    /**
     * 阈值类型枚举
     */
    public enum ThresholdType {
        LOWER_THAN, // 低于
        LESS_THAN_OR_EQUAL,
        GREATER_THAN, // 高于
        GREATER_THAN_OR_EQUAL,
        EQUALS, // 等于
        NOT_EQUALS // 不等于
    }

    /**
     * 时间单位枚举
     */
    public enum TimeUnit {
        SECONDS,
        MINUTES,
        HOURS,
        DAYS
    }

    /**
     * 违规级别
     */
    public enum ViolationLevel {
        CRITICAL,    // 严重
        WARNING,     // 警告
        INFO         // 信息
    }

    /**
     * SLA类型
     */
    public enum SlaType {
        AVAILABILITY,  // 可用性
        PERFORMANCE,   // 性能
        RELIABILITY,   // 可靠性
        CUSTOM         // 自定义
    }

    /**
     * 比较操作符
     */
    public enum ComparisonOperator {
        GREATER_THAN,          // 大于
        LESS_THAN,             // 小于
        GREATER_THAN_OR_EQUAL, // 大于等于
        LESS_THAN_OR_EQUAL,    // 小于等于
        EQUALS,                // 等于
        NOT_EQUALS             // 不等于
    }

    /**
     * SLA状态枚举
     */
    public enum SlaStatus {
        ACTIVE, // 激活
        INACTIVE, // 未激活
        MAINTENANCE // 维护中
    }

    /**
     * 严重程度枚举
     */
    public enum Severity {
        LOW,
        MEDIUM,
        HIGH,
        CRITICAL
    }

    /**
     * 获取格式化的目标描述
     */
    public String getFormattedTarget() {
        return String.format("%s %s %.2f",
            metricName,
            getOperatorSymbol(thresholdType),
            thresholdValue);
    }

    /**
     * 获取操作符符号
     */
    private String getOperatorSymbol(ThresholdType operator) {
        switch (operator) {
            case GREATER_THAN: return ">";
            case LOWER_THAN: return "<";
            case EQUALS: return "=";
            case NOT_EQUALS: return "!=";
            default: return "";
        }
    }

    /**
     * 检查值是否符合SLA
     */
    public boolean isSlaCompliant(double value) {
        switch (thresholdType) {
            case GREATER_THAN:
                return value > thresholdValue;
            case LOWER_THAN:
                return value < thresholdValue;
            case EQUALS:
                return Math.abs(value - thresholdValue) < 0.001; // 浮点数比较
            case NOT_EQUALS:
                return Math.abs(value - thresholdValue) >= 0.001;
            default:
                return false;
        }
    }

    /**
     * 获取违规级别
     */
    public ViolationLevel getViolationLevel(double value) {
        if (isSlaCompliant(value)) {
            return ViolationLevel.INFO;
        }

        double deviation = Math.abs(value - thresholdValue);
        double criticalThreshold = thresholdValue * 0.5; // 示例：偏离50%为严重

        if (deviation > criticalThreshold) {
            return ViolationLevel.CRITICAL;
        } else {
            return ViolationLevel.WARNING;
        }
    }

    // SlaService需要的方法

    /**
     * 获取阈值值
     */
    public Double getThresholdValue() {
        return this.thresholdValue;
    }

    /**
     * 获取阈值类型
     */
    public ThresholdType getThresholdType() {
        return this.thresholdType;
    }

    /**
     * 获取时间窗口
     */
    public Integer getTimeWindow() {
        return this.timeWindow != null ? this.timeWindow : 60;
    }

    /**
     * 获取时间单位
     */
    public TimeUnit getTimeUnit() {
        return this.timeUnit;
    }

    /**
     * 获取状态
     */
    public SlaStatus getStatus() {
        return this.enabled ? SlaStatus.ACTIVE : SlaStatus.INACTIVE;
    }

    /**
     * 设置状态
     */
    public void setStatus(SlaStatus status) {
        this.enabled = (status == SlaStatus.ACTIVE);
    }

    /**
     * 获取违规键
     */
    public String getViolationKey() {
        return String.format("%s:%s", serviceName, metricName);
    }
}
