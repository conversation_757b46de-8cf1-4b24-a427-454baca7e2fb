package com.talkweb.ai.crawler.sla;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * SLA定义仓库接口
 */
@Repository
public interface SlaDefinitionRepository extends JpaRepository<SlaDefinition, UUID> {

    /**
     * 根据服务名称查询SLA定义
     */
    List<SlaDefinition> findByServiceName(String serviceName);

    /**
     * 根据服务名称和指标名称查询SLA定义
     */
    List<SlaDefinition> findByServiceNameAndMetricName(String serviceName, String metricName);

    /**
     * 根据服务名称、指标名称和启用状态查询SLA定义
     */
    List<SlaDefinition> findByServiceNameAndMetricNameAndEnabled(String serviceName, String metricName, Boolean enabled);

    /**
     * 根据启用状态查询SLA定义
     */
    List<SlaDefinition> findByEnabled(Boolean enabled);

    /**
     * 根据SLA类型查询
     */
    List<SlaDefinition> findBySlaType(SlaDefinition.SlaType slaType);

    /**
     * 根据严重程度查询
     */
    List<SlaDefinition> findBySeverity(SlaDefinition.Severity severity);

    /**
     * 根据创建者查询
     */
    List<SlaDefinition> findByCreatedBy(String createdBy);

    /**
     * 查询指定时间范围内创建的SLA定义
     */
    List<SlaDefinition> findByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询指定时间范围内更新的SLA定义
     */
    List<SlaDefinition> findByUpdatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据服务名称和启用状态分页查询
     */
    Page<SlaDefinition> findByServiceNameAndEnabled(String serviceName, Boolean enabled, Pageable pageable);

    /**
     * 查询所有启用的SLA定义，按严重程度排序
     */
    @Query("SELECT s FROM SlaDefinition s WHERE s.enabled = true ORDER BY s.severity DESC, s.serviceName, s.metricName")
    List<SlaDefinition> findAllEnabledOrderBySeverity();

    /**
     * 统计每个服务的SLA定义数量
     */
    @Query("SELECT s.serviceName, COUNT(s) FROM SlaDefinition s WHERE s.enabled = true GROUP BY s.serviceName")
    List<Object[]> countByServiceName();

    /**
     * 统计每种SLA类型的数量
     */
    @Query("SELECT s.slaType, COUNT(s) FROM SlaDefinition s WHERE s.enabled = true GROUP BY s.slaType")
    List<Object[]> countBySlaType();

    /**
     * 查询即将过期的SLA定义 (根据更新时间)
     */
    @Query("SELECT s FROM SlaDefinition s WHERE s.enabled = true AND s.updatedAt < :cutoffDate")
    List<SlaDefinition> findStaleDefinitions(@Param("cutoffDate") LocalDateTime cutoffDate);
}
