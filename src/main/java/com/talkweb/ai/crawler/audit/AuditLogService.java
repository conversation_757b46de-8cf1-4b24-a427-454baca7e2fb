package com.talkweb.ai.crawler.audit;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * 审计日志服务
 * 提供异步审计日志记录、查询和分析功能
 */
@Slf4j
@Service
@Transactional
public class AuditLogService {
    
    @Autowired
    private AuditLogRepository auditLogRepository;
    
    @Autowired
    private AuditConfigurationProperties auditConfig;
    
    /**
     * 异步记录审计日志
     */
    @Async("auditLogExecutor")
    public CompletableFuture<AuditLog> logAsync(AuditLogBuilder builder) {
        try {
            if (!auditConfig.isEnabled()) {
                return CompletableFuture.completedFuture(null);
            }
            
            AuditLog auditLog = builder.build();
            
            // 自动设置风险级别
            if (auditLog.getRiskLevel() == null) {
                auditLog.setRiskLevel(determineRiskLevel(auditLog));
            }
            
            AuditLog saved = auditLogRepository.save(auditLog);
            
            // 高风险操作实时告警
            if (auditLog.getRiskLevel() == AuditLog.RiskLevel.HIGH || 
                auditLog.getRiskLevel() == AuditLog.RiskLevel.CRITICAL) {
                handleHighRiskEvent(saved);
            }
            
            log.debug("审计日志已记录: {}", saved.getId());
            return CompletableFuture.completedFuture(saved);
            
        } catch (Exception e) {
            log.error("记录审计日志失败", e);
            return CompletableFuture.failedFuture(e);
        }
    }
    
    /**
     * 同步记录审计日志
     */
    public AuditLog log(AuditLogBuilder builder) {
        try {
            return logAsync(builder).get();
        } catch (Exception e) {
            log.error("同步记录审计日志失败", e);
            throw new RuntimeException("审计日志记录失败", e);
        }
    }
    
    /**
     * 记录用户操作
     */
    public CompletableFuture<AuditLog> logUserAction(
            String userId, 
            String username,
            AuditLog.AuditAction action,
            AuditLog.ResourceType resourceType,
            String resourceId,
            String description,
            AuditLog.AuditResult result,
            HttpServletRequest request) {
        
        return logAsync(AuditLogBuilder.builder()
            .userId(userId)
            .username(username)
            .action(action)
            .resourceType(resourceType)
            .resourceId(resourceId)
            .description(description)
            .result(result)
            .ipAddress(getClientIpAddress(request))
            .userAgent(request.getHeader("User-Agent"))
            .sessionId(request.getSession().getId())
            .requestId(getRequestId(request)));
    }
    
    /**
     * 记录系统操作
     */
    public CompletableFuture<AuditLog> logSystemOperation(
            AuditLog.AuditAction action,
            String description,
            AuditLog.AuditResult result,
            String errorMessage) {
        
        return logAsync(AuditLogBuilder.builder()
            .userId("system")
            .username("System")
            .action(action)
            .resourceType(AuditLog.ResourceType.SYSTEM)
            .description(description)
            .result(result)
            .errorMessage(errorMessage)
            .riskLevel(AuditLog.RiskLevel.LOW));
    }
    
    /**
     * 记录API访问
     */
    public CompletableFuture<AuditLog> logApiAccess(
            String userId,
            String endpoint,
            String method,
            int statusCode,
            long durationMs,
            HttpServletRequest request) {
        
        AuditLog.AuditResult result = statusCode >= 200 && statusCode < 300 
            ? AuditLog.AuditResult.SUCCESS 
            : AuditLog.AuditResult.FAILURE;
            
        return logAsync(AuditLogBuilder.builder()
            .userId(userId)
            .action(AuditLog.AuditAction.API_ACCESS)
            .resourceType(AuditLog.ResourceType.API_ENDPOINT)
            .resourceId(method + " " + endpoint)
            .description(String.format("API访问: %s %s", method, endpoint))
            .result(result)
            .durationMs(durationMs)
            .ipAddress(getClientIpAddress(request))
            .userAgent(request.getHeader("User-Agent"))
            .additionalData(String.format("{\"statusCode\":%d,\"method\":\"%s\",\"endpoint\":\"%s\"}", 
                statusCode, method, endpoint)));
    }
    
    /**
     * 查询审计日志
     */
    @Transactional(readOnly = true)
    public Page<AuditLog> findAuditLogs(Specification<AuditLog> spec, Pageable pageable) {
        return auditLogRepository.findAll(spec, pageable);
    }
    
    /**
     * 根据用户查询
     */
    @Transactional(readOnly = true)
    public Page<AuditLog> findByUserId(String userId, Pageable pageable) {
        return auditLogRepository.findByUserId(userId, pageable);
    }
    
    /**
     * 根据操作类型查询
     */
    @Transactional(readOnly = true)
    public Page<AuditLog> findByAction(AuditLog.AuditAction action, Pageable pageable) {
        return auditLogRepository.findByActionEnum(action, pageable);
    }
    
    /**
     * 根据时间范围查询
     */
    @Transactional(readOnly = true)
    public Page<AuditLog> findByTimeRange(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable) {
        return auditLogRepository.findByTimestampBetween(startTime, endTime, pageable);
    }
    
    /**
     * 获取操作统计
     */
    @Transactional(readOnly = true)
    public List<AuditStatistics> getActionStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        return auditLogRepository.getActionStatistics(startTime, endTime);
    }
    
    /**
     * 获取用户活动统计
     */
    @Transactional(readOnly = true)
    public List<UserActivitySummary> getUserActivitySummary(LocalDateTime startTime, LocalDateTime endTime) {
        return auditLogRepository.getUserActivitySummary(startTime, endTime);
    }
    
    /**
     * 获取高风险操作
     */
    @Transactional(readOnly = true)
    public List<AuditLog> getHighRiskOperations(LocalDateTime startTime, LocalDateTime endTime) {
        return auditLogRepository.findByRiskLevelInAndTimestampBetween(
            List.of(AuditLog.RiskLevel.HIGH, AuditLog.RiskLevel.CRITICAL),
            startTime, endTime);
    }
    
    /**
     * 删除过期日志
     */
    @Transactional
    public int cleanupExpiredLogs() {
        if (auditConfig.getDataRetention() <= 0) {
            return 0;
        }
        
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(auditConfig.getDataRetention());
        int deletedCount = auditLogRepository.deleteByTimestampBefore(cutoffDate);
        
        log.info("清理过期审计日志: {} 条记录已删除", deletedCount);
        return deletedCount;
    }
    
    /**
     * 确定风险级别
     */
    private AuditLog.RiskLevel determineRiskLevel(AuditLog auditLog) {
        // 失败的认证操作
        if (auditLog.getAction() == AuditLog.AuditAction.LOGIN_FAILED) {
            return AuditLog.RiskLevel.MEDIUM;
        }
        
        // 系统管理操作
        if (auditLog.getAction() == AuditLog.AuditAction.SYSTEM_STOP ||
            auditLog.getAction() == AuditLog.AuditAction.SYSTEM_RESTART) {
            return AuditLog.RiskLevel.HIGH;
        }
        
        // 数据删除操作
        if (auditLog.getAction() == AuditLog.AuditAction.DATA_DELETE) {
            return AuditLog.RiskLevel.HIGH;
        }
        
        // 配置修改
        if (auditLog.getAction() == AuditLog.AuditAction.CONFIG_UPDATE) {
            return AuditLog.RiskLevel.MEDIUM;
        }
        
        // 权限操作
        if (auditLog.getAction() == AuditLog.AuditAction.PERMISSION_GRANT ||
            auditLog.getAction() == AuditLog.AuditAction.PERMISSION_REVOKE) {
            return AuditLog.RiskLevel.HIGH;
        }
        
        // 失败的操作
        if (auditLog.getResult() == AuditLog.AuditResult.FAILURE ||
            auditLog.getResult() == AuditLog.AuditResult.ERROR) {
            return AuditLog.RiskLevel.MEDIUM;
        }
        
        // 未授权访问
        if (auditLog.getResult() == AuditLog.AuditResult.UNAUTHORIZED ||
            auditLog.getResult() == AuditLog.AuditResult.FORBIDDEN) {
            return AuditLog.RiskLevel.HIGH;
        }
        
        return AuditLog.RiskLevel.LOW;
    }
    
    /**
     * 处理高风险事件
     */
    private void handleHighRiskEvent(AuditLog auditLog) {
        // 实现告警逻辑
        log.warn("检测到高风险操作: 用户={}, 操作={}, 资源={}, 结果={}", 
            auditLog.getUsername(), auditLog.getAction(), 
            auditLog.getResourceId(), auditLog.getResult());
        
        // 可以集成邮件、短信、Webhook等告警方式
        // alertService.sendHighRiskAlert(auditLog);
    }
    
    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        if (request == null) return null;
        
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
    
    /**
     * 获取请求ID
     */
    private String getRequestId(HttpServletRequest request) {
        if (request == null) return null;
        
        String requestId = request.getHeader("X-Request-ID");
        if (requestId == null) {
            requestId = (String) request.getAttribute("requestId");
        }
        
        return requestId != null ? requestId : UUID.randomUUID().toString();
    }
}