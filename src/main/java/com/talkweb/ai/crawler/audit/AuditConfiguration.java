package com.talkweb.ai.crawler.audit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * 审计配置
 * 配置审计日志相关的Bean和定时任务
 */
@Configuration
@EnableAspectJAutoProxy
@EnableAsync
@EnableScheduling
@ConditionalOnProperty(name = "crawler.audit.enabled", havingValue = "true", matchIfMissing = true)
public class AuditConfiguration {
    
    @Autowired
    private AuditConfigurationProperties auditConfig;
    
    @Autowired
    private AuditLogService auditLogService;
    
    /**
     * 审计日志异步执行器
     */
    @Bean("auditLogExecutor")
    public Executor auditLogExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(auditConfig.getAsyncThreadPoolSize());
        executor.setMaxPoolSize(auditConfig.getAsyncThreadPoolSize() * 2);
        executor.setQueueCapacity(1000);
        executor.setThreadNamePrefix("audit-log-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        return executor;
    }
    
    /**
     * 定时清理过期审计日志
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * *")
    @ConditionalOnProperty(name = "crawler.audit.retention-days", havingValue = "0", matchIfMissing = false)
    public void cleanupExpiredAuditLogs() {
        if (auditConfig.getDataRetention() > 0) {
            auditLogService.cleanupExpiredLogs();
        }
    }
}