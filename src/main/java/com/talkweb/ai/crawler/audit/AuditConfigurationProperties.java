package com.talkweb.ai.crawler.audit;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 审计配置属性
 */
@Data
@ConfigurationProperties(prefix = "crawler.audit")
public class AuditConfigurationProperties {
    
    private boolean enabled = true;
    private int dataRetention = 365; // 数据保留天数
    private boolean alerting = true;
    private int asyncThreadPoolSize = 4;
    
    // 数据保留配置
    private DataRetention dataRetentionConfig = new DataRetention();
    
    // 告警配置
    private Alerting alertingConfig = new Alerting();
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public int getDataRetention() {
        return dataRetention;
    }
    
    public void setDataRetention(int dataRetention) {
        this.dataRetention = dataRetention;
    }
    
    public boolean getAlerting() {
        return alerting;
    }
    
    public int getAsyncThreadPoolSize() {
        return asyncThreadPoolSize;
    }
    
    public DataRetention getDataRetentionConfig() {
        return dataRetentionConfig;
    }
    
    public Alerting getAlertingConfig() {
        return alertingConfig;
    }
    
    @Data
    public static class DataRetention {
        private int retentionDays = 365;
        private boolean autoCleanupEnabled = true;
    }
    
    @Data 
    public static class Alerting {
        private int securityEventThreshold = 10;
        private int operationFrequencyThreshold = 100;
    }
}