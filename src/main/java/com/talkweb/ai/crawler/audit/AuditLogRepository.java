package com.talkweb.ai.crawler.audit;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 审计日志仓库接口
 * 提供审计日志的数据访问功能
 */
@Repository
public interface AuditLogRepository extends JpaRepository<AuditLog, UUID>, JpaSpecificationExecutor<AuditLog> {
    
    /**
     * 根据用户ID查询
     */
    Page<AuditLog> findByUserId(String userId, Pageable pageable);
    
    /**
     * 根据操作类型查询
     */
    Page<AuditLog> findByActionEnum(AuditLog.AuditAction actionEnum, Pageable pageable);
    
    /**
     * 根据时间范围查询
     */
    Page<AuditLog> findByTimestampBetween(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);
    
    /**
     * 根据风险级别和时间范围查询
     */
    List<AuditLog> findByRiskLevelInAndTimestampBetween(
        List<AuditLog.RiskLevel> riskLevels, 
        LocalDateTime startTime, 
        LocalDateTime endTime);
    
    /**
     * 根据用户和时间范围查询
     */
    List<AuditLog> findByUserIdAndTimestampBetween(
        String userId, 
        LocalDateTime startTime, 
        LocalDateTime endTime);
    
    /**
     * 根据资源类型查询
     */
    Page<AuditLog> findByResourceType(AuditLog.ResourceType resourceType, Pageable pageable);
    
    /**
     * 根据IP地址查询
     */
    List<AuditLog> findByIpAddress(String ipAddress);
    
    /**
     * 查询失败的操作
     */
    @Query("SELECT a FROM AuditLog a WHERE a.result IN ('FAILURE', 'ERROR', 'UNAUTHORIZED', 'FORBIDDEN') " +
           "AND a.timestamp BETWEEN :startTime AND :endTime ORDER BY a.timestamp DESC")
    List<AuditLog> findFailedOperations(@Param("startTime") LocalDateTime startTime, 
                                       @Param("endTime") LocalDateTime endTime);
    
    /**
     * 获取操作统计
     */
    @Query("SELECT new com.talkweb.ai.crawler.audit.AuditStatistics(a.actionEnum, COUNT(a), " +
           "SUM(CASE WHEN a.result = 'SUCCESS' THEN 1 ELSE 0 END), " +
           "SUM(CASE WHEN a.result IN ('FAILURE', 'ERROR') THEN 1 ELSE 0 END)) " +
           "FROM AuditLog a WHERE a.timestamp BETWEEN :startTime AND :endTime " +
           "GROUP BY a.actionEnum ORDER BY COUNT(a) DESC")
    List<AuditStatistics> getActionStatistics(@Param("startTime") LocalDateTime startTime, 
                                             @Param("endTime") LocalDateTime endTime);
    
    /**
     * 获取用户活动统计
     */
    @Query("SELECT new com.talkweb.ai.crawler.audit.UserActivitySummary(a.userId, a.username, " +
           "COUNT(a), MIN(a.timestamp), MAX(a.timestamp), " +
           "COUNT(DISTINCT a.ipAddress)) " +
           "FROM AuditLog a WHERE a.timestamp BETWEEN :startTime AND :endTime " +
           "AND a.userId IS NOT NULL " +
           "GROUP BY a.userId, a.username ORDER BY COUNT(a) DESC")
    List<UserActivitySummary> getUserActivitySummary(@Param("startTime") LocalDateTime startTime, 
                                                    @Param("endTime") LocalDateTime endTime);
    
    /**
     * 获取IP活动统计
     */
    @Query("SELECT a.ipAddress, COUNT(a), COUNT(DISTINCT a.userId) " +
           "FROM AuditLog a WHERE a.timestamp BETWEEN :startTime AND :endTime " +
           "AND a.ipAddress IS NOT NULL " +
           "GROUP BY a.ipAddress HAVING COUNT(a) > :threshold " +
           "ORDER BY COUNT(a) DESC")
    List<Object[]> getIpActivityStatistics(@Param("startTime") LocalDateTime startTime, 
                                          @Param("endTime") LocalDateTime endTime,
                                          @Param("threshold") long threshold);
    
    /**
     * 查询异常登录
     */
    @Query("SELECT a FROM AuditLog a WHERE a.actionEnum = 'LOGIN_FAILED' " +
           "AND a.timestamp BETWEEN :startTime AND :endTime " +
           "GROUP BY a.userId, a.ipAddress " +
           "HAVING COUNT(a) >= :threshold")
    List<AuditLog> findSuspiciousLoginAttempts(@Param("startTime") LocalDateTime startTime, 
                                              @Param("endTime") LocalDateTime endTime,
                                              @Param("threshold") int threshold);
    
    /**
     * 查询长时间操作 - 基于时间戳差异估算
     */
    @Query("SELECT a FROM AuditLog a WHERE a.timestamp BETWEEN :startTime AND :endTime " +
           "ORDER BY a.timestamp DESC")
    List<AuditLog> findLongRunningOperations(@Param("thresholdMs") long thresholdMs,
                                            @Param("startTime") LocalDateTime startTime, 
                                            @Param("endTime") LocalDateTime endTime);
    
    /**
     * 删除过期日志
     */
    @Modifying
    @Query("DELETE FROM AuditLog a WHERE a.timestamp < :expireTime")
    int deleteExpiredLogs(@Param("expireTime") LocalDateTime expireTime);
    
    /**
     * 统计每日操作数量
     */
    @Query("SELECT DATE(a.timestamp), COUNT(a) FROM AuditLog a " +
           "WHERE a.timestamp BETWEEN :startTime AND :endTime " +
           "GROUP BY DATE(a.timestamp) ORDER BY DATE(a.timestamp)")
    List<Object[]> getDailyOperationCounts(@Param("startTime") LocalDateTime startTime, 
                                          @Param("endTime") LocalDateTime endTime);
    
    /**
     * 删除过期日志
     */
    @Modifying
    @Query("DELETE FROM AuditLog a WHERE a.timestamp < :cutoffDate")
    int deleteByTimestampBefore(@Param("cutoffDate") LocalDateTime cutoffDate);
    
    /**
     * 统计总记录数
     */
    @Query("SELECT COUNT(a) FROM AuditLog a WHERE a.timestamp BETWEEN :startTime AND :endTime")
    long countByTimestampBetween(@Param("startTime") LocalDateTime startTime, 
                                @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查询最近的用户操作
     */
    @Query("SELECT a FROM AuditLog a WHERE a.userId = :userId " +
           "ORDER BY a.timestamp DESC LIMIT 10")
    List<AuditLog> findRecentUserOperations(@Param("userId") String userId);
    
    // AuditService需要的方法
    
    /**
     * 根据时间范围查询（无分页）
     */
    List<AuditLog> findByTimestampBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据操作和时间范围查询
     */
    List<AuditLog> findByActionEnumAndTimestampBetween(AuditLog.AuditAction actionEnum, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据资源类型和ID查询
     */
    List<AuditLog> findByResourceTypeAndResourceIdAndTimestampBetween(
        String resourceType, String resourceId, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 查询安全事件
     */
    @Query("SELECT a FROM AuditLog a WHERE (a.category IN ('AUTHENTICATION', 'AUTHORIZATION', 'SECURITY') OR " +
           "a.severity IN ('ERROR', 'CRITICAL')) AND a.timestamp BETWEEN :startTime AND :endTime")
    List<AuditLog> findSecurityEvents(@Param("startTime") LocalDateTime startTime, 
                                     @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计事件数量
     */
    @Query("SELECT COUNT(a) FROM AuditLog a WHERE a.timestamp BETWEEN :startTime AND :endTime")
    long countEventsBetween(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 按类别统计
     */
    @Query("SELECT a.category, COUNT(a) FROM AuditLog a WHERE a.timestamp BETWEEN :startTime AND :endTime GROUP BY a.category")
    List<Object[]> countEventsByCategory(@Param("startTime") LocalDateTime startTime, 
                                        @Param("endTime") LocalDateTime endTime);
    
    /**
     * 按严重程度统计
     */
    @Query("SELECT a.severity, COUNT(a) FROM AuditLog a WHERE a.timestamp BETWEEN :startTime AND :endTime GROUP BY a.severity")
    List<Object[]> countEventsBySeverity(@Param("startTime") LocalDateTime startTime, 
                                        @Param("endTime") LocalDateTime endTime);
    
    /**
     * 按用户统计
     */
    @Query("SELECT a.userId, COUNT(a) FROM AuditLog a WHERE a.timestamp BETWEEN :startTime AND :endTime GROUP BY a.userId")
    List<Object[]> countEventsByUser(@Param("startTime") LocalDateTime startTime, 
                                    @Param("endTime") LocalDateTime endTime);
    
    /**
     * 按操作统计
     */
    @Query("SELECT a.actionText, COUNT(a) FROM AuditLog a WHERE a.timestamp BETWEEN :startTime AND :endTime GROUP BY a.actionText")
    List<Object[]> countEventsByAction(@Param("startTime") LocalDateTime startTime, 
                                      @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计安全事件数量
     */
    @Query("SELECT COUNT(a) FROM AuditLog a WHERE (a.category IN ('AUTHENTICATION', 'AUTHORIZATION', 'SECURITY') OR " +
           "a.severity IN ('ERROR', 'CRITICAL')) AND a.timestamp BETWEEN :startTime AND :endTime")
    long countSecurityEvents(@Param("startTime") LocalDateTime startTime, 
                            @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计失败操作数量
     */
    @Query("SELECT COUNT(a) FROM AuditLog a WHERE (a.status = 'FAILURE' OR a.severity IN ('ERROR', 'CRITICAL')) " +
           "AND a.timestamp BETWEEN :startTime AND :endTime")
    long countFailedOperations(@Param("startTime") LocalDateTime startTime, 
                              @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计用户操作数量
     */
    long countByUserIdAndTimestampBetween(String userId, LocalDateTime startTime, LocalDateTime endTime);
    
}