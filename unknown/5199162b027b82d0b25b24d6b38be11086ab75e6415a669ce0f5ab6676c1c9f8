package com.talkweb.ai.crawler.config;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.prometheus.PrometheusConfig;
import io.micrometer.prometheus.PrometheusMeterRegistry;
import io.micrometer.core.instrument.Clock;
import io.micrometer.core.instrument.config.MeterFilter;
import io.micrometer.core.instrument.distribution.DistributionStatisticConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.time.Duration;

/**
 * Prometheus监控配置增强
 * 提供完整的Prometheus集成和自定义指标配置
 */
@Configuration
@ConditionalOnProperty(name = "management.metrics.export.prometheus.enabled", havingValue = "true", matchIfMissing = true)
public class PrometheusConfiguration {

    private static final Logger logger = LoggerFactory.getLogger(PrometheusConfiguration.class);

    /**
     * 配置Prometheus MeterRegistry
     */
    @Bean
    @Primary
    public PrometheusMeterRegistry prometheusMeterRegistry() {
        PrometheusConfig prometheusConfig = new PrometheusConfig() {
            @Override
            public Duration step() {
                return Duration.ofSeconds(10); // 10秒采样间隔
            }

            @Override
            public String get(String key) {
                return null; // 使用默认配置
            }
        };

        PrometheusMeterRegistry registry = new PrometheusMeterRegistry(prometheusConfig);

        logger.info("Prometheus MeterRegistry 已配置，采样间隔: {} 秒", prometheusConfig.step().getSeconds());
        return registry;
    }

    /**
     * 自定义MeterRegistry配置
     */
    @Bean
    public MeterRegistryCustomizer<MeterRegistry> metricsCommonTags() {
        return registry -> {
            // 添加全局标签
            registry.config()
                .commonTags(
                    "application", "web-crawler",
                    "version", "1.0.0",
                    "environment", getEnvironment()
                )
                // 配置指标过滤器
                .meterFilter(MeterFilter.deny(id -> {
                    // 排除一些不需要的JVM指标
                    String name = id.getName();
                    return name.startsWith("jvm.gc.pause") && name.contains("unknown");
                }))
                // 配置直方图和百分位数
                .meterFilter(MeterFilter.accept(id -> {
                    String name = id.getName();
                    return name.startsWith("crawler.") ||
                           name.startsWith("http.server.requests") ||
                           name.startsWith("system.") ||
                           name.startsWith("process.");
                }))
                // 为HTTP请求配置直方图
                .meterFilter(MeterFilter.accept(id -> true));

            logger.info("MeterRegistry 全局配置已应用");
        };
    }

    /**
     * 配置自定义分布统计
     */
    @Bean
    public MeterRegistryCustomizer<PrometheusMeterRegistry> prometheusRegistryCustomizer() {
        return registry -> {
            registry.config()
                // 为爬虫相关指标配置百分位数
                .meterFilter(MeterFilter.accept(id -> {
                    String name = id.getName();
                    if (name.startsWith("crawler.")) {
                        return true;
                    }
                    return !name.contains("unknown");
                }))
                // 配置直方图桶
                .meterFilter(new MeterFilter() {
                    @Override
                    public DistributionStatisticConfig configure(io.micrometer.core.instrument.Meter.Id id, DistributionStatisticConfig config) {
                        String name = id.getName();
                        if (name.startsWith("crawler.fetch.duration")) {
                            return DistributionStatisticConfig.builder()
                                .percentiles(0.5, 0.75, 0.95, 0.99) // 50%, 75%, 95%, 99%百分位数
                                .serviceLevelObjectives(
                                    Duration.ofMillis(100).toNanos(),   // 100ms
                                    Duration.ofMillis(500).toNanos(),   // 500ms
                                    Duration.ofSeconds(1).toNanos(),    // 1s
                                    Duration.ofSeconds(5).toNanos(),    // 5s
                                    Duration.ofSeconds(10).toNanos()    // 10s
                                )
                                .minimumExpectedValue(Duration.ofMillis(1).toNanos())
                                .maximumExpectedValue(Duration.ofMinutes(1).toNanos())
                                .build()
                                .merge(config);
                        }
                        if (name.startsWith("crawler.content.size")) {
                            return DistributionStatisticConfig.builder()
                                .percentiles(0.5, 0.95, 0.99)
                                .serviceLevelObjectives(1_000, 10_000, 100_000, 1_000_000) // 1KB, 10KB, 100KB, 1MB
                                .build()
                                .merge(config);
                        }
                        return config;
                    }
                });

            logger.info("Prometheus 自定义分布统计配置已应用");
        };
    }

    /**
     * 爬虫特定的指标配置Bean
     */
    @Bean
    public CrawlerMetricsConfiguration crawlerMetricsConfiguration() {
        return new CrawlerMetricsConfiguration();
    }

    /**
     * 获取当前环境
     */
    private String getEnvironment() {
        String profiles = System.getProperty("spring.profiles.active", "default");
        if (profiles.contains("prod")) {
            return "production";
        } else if (profiles.contains("test")) {
            return "testing";
        } else if (profiles.contains("dev")) {
            return "development";
        } else {
            return "default";
        }
    }

    /**
     * 爬虫指标配置类
     * 提供指标名称常量和配置参数
     */
    public static class CrawlerMetricsConfiguration {

        // 指标名称常量
        public static final String FETCH_DURATION = "crawler.fetch.duration";
        public static final String FETCH_TOTAL = "crawler.fetch.total";
        public static final String FETCH_ERRORS = "crawler.fetch.errors";
        public static final String CONTENT_SIZE = "crawler.content.size";
        public static final String CACHE_HITS = "crawler.cache.hits";
        public static final String CACHE_MISSES = "crawler.cache.misses";
        public static final String QUEUE_SIZE = "crawler.queue.size";
        public static final String ACTIVE_CONNECTIONS = "crawler.connections.active";
        public static final String PAGES_ANALYZED = "crawler.pages.analyzed";
        public static final String CONTENT_QUALITY = "crawler.content.quality";

        // 业务指标
        public static final String BUSINESS_VALUE_INDEX = "crawler.business.value_index";
        public static final String PERFORMANCE_INDEX = "crawler.performance.overall_index";
        public static final String RESOURCE_UTILIZATION = "crawler.resources.utilization_index";

        // 系统健康指标
        public static final String HEALTH_SCORE = "crawler.health.overall_score";
        public static final String OPTIMIZATION_EFFECTIVENESS = "crawler.optimization.effectiveness";

        // 标签常量
        public static final String TAG_DOMAIN = "domain";
        public static final String TAG_STATUS = "status";
        public static final String TAG_CONTENT_TYPE = "content_type";
        public static final String TAG_CACHE_LEVEL = "cache_level";
        public static final String TAG_ERROR_TYPE = "error_type";
        public static final String TAG_OPTIMIZATION_TYPE = "optimization_type";

        public CrawlerMetricsConfiguration() {
            logger.info("CrawlerMetricsConfiguration 已初始化");
        }

        /**
         * 获取所有爬虫指标名称
         */
        public String[] getAllMetricNames() {
            return new String[]{
                FETCH_DURATION, FETCH_TOTAL, FETCH_ERRORS, CONTENT_SIZE,
                CACHE_HITS, CACHE_MISSES, QUEUE_SIZE, ACTIVE_CONNECTIONS,
                PAGES_ANALYZED, CONTENT_QUALITY, BUSINESS_VALUE_INDEX,
                PERFORMANCE_INDEX, RESOURCE_UTILIZATION, HEALTH_SCORE,
                OPTIMIZATION_EFFECTIVENESS
            };
        }

        /**
         * 获取默认标签
         */
        public String[] getDefaultTags() {
            return new String[]{
                "application", "web-crawler",
                "component", "crawler-core"
            };
        }
    }
}
